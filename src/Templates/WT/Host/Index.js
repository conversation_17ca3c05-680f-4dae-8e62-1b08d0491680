import React from 'react';
import Main from "./Main"
import { connect } from 'react-redux';
import * as HostActions from "../../../Actions/HostAction"
import  Firebase  from '../../../config/Firebase';
import ButtonClick from "../../../assets/click.mp3"
import * as ExceptionActions from "../../../Actions/Exception"
import { Modalexception } from '../../../components/Exceptions';
import * as AuthException from "../../../Actions/Auth"
import { GetRequestWithHeaders } from '../../../Tools/helpers/api';
import * as Sessions from '../../../Actions/Sessions';
import { getAuth,onAuthStateChanged  } from "firebase/auth";
import MeetingScreen from '../../../Tools/ToolComponents/MeetingScreen';


class Index extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      isAuthChecked: false,
      isOpen:true
    };
    
    this.props.CheckInternetSpeed();
    this.props.SetLocalStream(this.props.DummyAudio);
    // checking thr auth of user 
    const auth = getAuth();
    onAuthStateChanged(auth, (user) => {
      this.setState({ isAuthChecked: true });
      
      if (!user) {
        const currentPath = window.location.pathname;
        const redirectUrl = `/login?redirect_uri=${encodeURIComponent(currentPath)}`;
        window.location.href = redirectUrl;
        return;
      }
      
      this.setupFirestoreListener(user);
    });
  }


  setupFirestoreListener = (user) => {
    Firebase.firestore().collection('sessions').doc(this.props.id).onSnapshot(
      { includeMetadataChanges: true },
      async (doc) => {
        if (!doc.exists) {
          window.location = "/projects";
          return;
        }

        const room_data = doc.data();
        this.props.SetSessions(room_data);
        
        try {
          const response = await GetRequestWithHeaders({
            url: process.env.REACT_APP_API_BACKEND_API_URL + `project/GetProject/${room_data.project_id}`,
          });
          this.props.SetProjectDetails(response.data);
        } catch (error) {
          console.error("Error fetching project details:", error);
        }

        if (room_data.status === "ended") {
          window.location = "/projects";
          return;
        } else if (room_data.status === "created") {
          Firebase.firestore().collection("sessions").doc(this.props.id).update({
            status: 'on-going'
          });
        }

        if (!this.props.Auth) {
          this.props.CheckAuth();
        }

        if (room_data.user_id !== user.uid) {
          window.location = "/login";
          return;
        }
      },
      (error) => {
        console.error("Error listening to session:", error);
        this.props.SetModalException("Error accessing session data");
      }
    );
  }
  toggleModal = (val) => {
    // this.setState({ isOpen: val });
  }
  render() {
    if (!this.state.isAuthChecked) {
      return (
        <div style={{ width: "100%", height: "100%" }}>
          <div style={{
            margin: "auto",
            width: 50,
            position: "relative",
            height: 50,
            top: "calc(50% - 80px)"
          }}>
            <div className="lds-ring">
              <div />
              <div />
              <div />
              <div />
            </div>
          </div>
        </div>
      );
    }
    if (this.props.Auth && this.props.LocalStream && this.props.SessionDetails) {
      return (
        <>
          <video style={{ display: "none" }} autoPlay controls name="media">
            <source src={ButtonClick}></source>
          </video>
          {this.props.ModalException ? 
            <Modalexception 
              onclick={() => this.props.SetModalException(false)}
              message={this.props.ModalException} 
            /> 
            : <></>}
         
              <Main roomId={this.props.id} pid={this.props.id} />
            
        </>
      );
    }

    return (
      <>
        {this.props.ModalException ? 
          <Modalexception 
            onclick={() => this.props.SetModalException(false)}
            message={this.props.ModalException} 
          /> 
          : <></>}
        <div style={{ width: "100%", height: "100%" }}>
          <div style={{
            margin: "auto",
            width: 50,
            position: "relative",
            height: 50,
            top: "calc(50% - 80px)"
          }}>
            <div className="lds-ring">
              <div />
              <div />
              <div />
              <div />
            </div>
          </div>
        </div>
      </>
    );
  }
}

const mapStateToProps = (state) => {
  return {
    Peers: state.Call.peers,
    ModalException: state.Exception.modalexception,
    DummyAudio: state.Call.DummyAudio,
    LocalStream: state.Call.LocalStream,
    Video: state.Call.Video,
    Audio: state.Call.Audio,
    Auth: state.Auth.auth,
    ProjectDetails: state.Sessions.projectDetails,
    SessionDetails: state.Sessions.sessions,
    SessionConfig: state.Sessions.config,
  }
}

const mapDispatchToProps = {
  ...HostActions,
  ...ExceptionActions,
  ...AuthException,
  ...Sessions,
}

export default connect(mapStateToProps, mapDispatchToProps)(Index);