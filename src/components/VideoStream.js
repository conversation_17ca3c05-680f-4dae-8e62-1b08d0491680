import React from 'react'
import {connect} from "react-redux";
import * as HostActions from "../Actions/HostAction"
class VideoStream extends React.PureComponent {
constructor(props){
  super(props);
  this.videoRef = React.createRef()
  this.HandleVideo=this.HandleVideo.bind(this);
  this.HandleAudio=this.HandleAudio.bind(this);
}
  componentDidMount () {
    const video = this.videoRef.current

    if ('srcObject' in video) {
      video.srcObject = this.props.LocalStream
    } else {
      video.src = window.URL.createObjectURL(this.props.LocalStream) // for older browsers
    }
  }
  HandleVideo(){

    if(this.props.Video){
    this.props.LocalStream.removeTrack(this.props.LocalStream.getVideoTracks()[0])
    this.props.LocalStream.addTrack(HostActions.canvastrack)
    this.props.ToogleLocalAV("Video",false)
    }
    else{
    navigator.mediaDevices.getUserMedia({video:true}).then(stream=>{
    this.props.LocalStream.removeTrack(this.props.LocalStream.getVideoTracks()[0])
    this.props.LocalStream.addTrack(stream.getVideoTracks()[0]);
    this.props.ToogleLocalAV("Video",true)
    })
    }
    }
    HandleAudio(){
      if(this.props.Audio){
        this.props.LocalStream.getAudioTracks()[0].enabled=false;
        this.props.ToogleLocalAV("Audio",false)
        }
        else{

          this.props.LocalStream.getAudioTracks()[0].enabled=true;
          this.props.ToogleLocalAV("Audio",true)

        }
    }
    Strength(position,network){
      switch (network.strength) {
        case "WEEK":
          if(position<=1){
            return "red"
          }else{
            return "#ff00006e"
          }
          case "MODERATE":
            if(position<=2){
              return "#f57a06"
            }else{
              return "#f57a068f"
            }
          case "BETTER":
            if(position<=3){
              return "#3fff08"
            }else{
              return "#3fff08"
            }
            case "EXCELLENT":
            if(position<=3){
              return "#3fff08"
            }else{
              return "#3fff08"
            }
        default:
          return "#fff"
      }
    }
  componentDidUpdate () {


  }

  render () {

    return (<>

      <div className='h-fit w-full'>
  <div className='w-full h-auto pt-[56.25%] relative'>
   <video className="rounded-[10px] left-0 top-0 absolute w-full h-full object-none"
          autoPlay muted={true}
          ref={this.videoRef}
        />
        <div
         className='flex flex-row absolute bottom-1 lg:bottom-3 left-1/2 -translate-x-2/4 bg-[#2e2e2e] rounded p-2'>
      <div className="mr-3 flex justify-end " >
      <div  className="relative border-none rounded bg-transparent cursor-pointer" onClick={this.HandleAudio}>
         {this.props.Audio?
<svg xmlns="http://www.w3.org/2000/svg" className='w-6 h-6 fill-white' viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="mic"><rect  opacity="0"/><path d="M12 15a4 4 0 0 0 4-4V6a4 4 0 0 0-8 0v5a4 4 0 0 0 4 4z"/><path d="M19 11a1 1 0 0 0-2 0 5 5 0 0 1-10 0 1 1 0 0 0-2 0 7 7 0 0 0 6 6.92V20H8.89a.89.89 0 0 0-.89.89v.22a.89.89 0 0 0 .89.89h6.22a.89.89 0 0 0 .89-.89v-.22a.89.89 0 0 0-.89-.89H13v-2.08A7 7 0 0 0 19 11z"/></g></g></svg>
:
<svg xmlns="http://www.w3.org/2000/svg" className='w-6 h-6 fill-white' viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="mic-off"><rect opacity="0"/><path d="M15.58 12.75A4 4 0 0 0 16 11V6a4 4 0 0 0-7.92-.75"/><path d="M19 11a1 1 0 0 0-2 0 4.86 4.86 0 0 1-.69 2.48L17.78 15A7 7 0 0 0 19 11z"/><path d="M12 15h.16L8 10.83V11a4 4 0 0 0 4 4z"/><path d="M20.71 19.29l-16-16a1 1 0 0 0-1.42 1.42l16 16a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42z"/><path d="M15 20h-2v-2.08a7 7 0 0 0 1.65-.44l-1.6-1.6A4.57 4.57 0 0 1 12 16a5 5 0 0 1-5-5 1 1 0 0 0-2 0 7 7 0 0 0 6 6.92V20H9a1 1 0 0 0 0 2h6a1 1 0 0 0 0-2z"/></g></g></svg>
  }
         </div>
      </div>
      <div >
      <div onClick={this.HandleVideo} className="rounded relative  border-[none] bg-transparent cursor-pointer">

         {this.props.Video?

<svg xmlns="http://www.w3.org/2000/svg" className='w-6 h-6 fill-white' viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="video"><rect  opacity="0"/><path d="M21 7.15a1.7 1.7 0 0 0-1.85.3l-2.15 2V8a3 3 0 0 0-3-3H5a3 3 0 0 0-3 3v8a3 3 0 0 0 3 3h9a3 3 0 0 0 3-3v-1.45l2.16 2a1.74 1.74 0 0 0 1.16.45 1.68 1.68 0 0 0 .69-.15 1.6 1.6 0 0 0 1-1.48V8.63A1.6 1.6 0 0 0 21 7.15z"/></g></g></svg>:
<svg xmlns="http://www.w3.org/2000/svg" className='w-6 h-6 fill-white' viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="video-off"><rect opacity="0"/><path d="M14.22 17.05L4.88 7.71 3.12 6 3 5.8A3 3 0 0 0 2 8v8a3 3 0 0 0 3 3h9a2.94 2.94 0 0 0 1.66-.51z"/><path d="M21 7.15a1.7 1.7 0 0 0-1.85.3l-2.15 2V8a3 3 0 0 0-3-3H7.83l1.29 1.29 6.59 6.59 2 2 2 2a1.73 1.73 0 0 0 .6.11 1.68 1.68 0 0 0 .69-.15 1.6 1.6 0 0 0 1-1.48V8.63a1.6 1.6 0 0 0-1-1.48z"/><path d="M17 15.59l-2-2L8.41 7l-2-2-1.7-1.71a1 1 0 0 0-1.42 1.42l.54.53L5.59 7l9.34 9.34 1.46 1.46 2.9 2.91a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42z"/></g></g></svg>
}


         </div>
   </div>
</div>
</div>

</div>
 </>
    )
  }
}
const mapStateToProps = state => {
  return {
    LocalStream:state.Call.LocalStream,
    Video:state.Call.Video,
    Audio:state.Call.Audio,
    NetWorkSpeed:state.Call.NetWorkSpeed,
    DummyAudio:state.Call.DummyAudio
  }
}

const mapDispatchToProps = {
  ...HostActions
}

export default connect(mapStateToProps, mapDispatchToProps)(VideoStream)